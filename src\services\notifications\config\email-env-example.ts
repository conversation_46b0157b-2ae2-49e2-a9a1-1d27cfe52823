/**
 * Exemplo de configuração de variáveis de ambiente para o sistema de e-mail
 * Copie estas variáveis para seu arquivo .env
 */

export const EMAIL_ENV_VARIABLES = {
  // Provedor de e-mail principal (resend ou aws_ses)
  EMAIL_PROVIDER: 'resend',

  // Configurações do Resend
  RESEND_API_KEY: '',
  RESEND_FROM_DOMAIN: '',
  RESEND_WEBHOOK_SECRET: 'your_webhook_secret_here',

  // Configurações do AWS SES (alternativa ao Resend)
  AWS_ACCESS_KEY_ID: 'your_aws_access_key_here',
  AWS_SECRET_ACCESS_KEY: 'your_aws_secret_key_here',
  AWS_REGION: 'us-east-1',
  AWS_SES_FROM_DOMAIN: 'meusaas.com',

  // Configurações do WhatsApp (Evolution API)
  EVOLUTION_API_URL: 'https://your-evolution-api.com',
  EVOLUTION_API_KEY: 'your_evolution_api_key_here',
  EVOLUTION_INSTANCE_NAME: 'apexsaas-whatsapp',
  EVOLUTION_WEBHOOK_SECRET: 'your_evolution_webhook_secret_here'
};

/**
 * Instruções para configuração:
 * 
 * 1. RESEND (Recomendado):
 *    - Crie uma conta em https://resend.com
 *    - Obtenha sua API key no dashboard
 *    - Configure seu domínio personalizado
 *    - Adicione as variáveis RESEND_* ao seu .env
 * 
 * 2. AWS SES (Alternativa):
 *    - Configure uma conta AWS
 *    - Ative o Amazon SES
 *    - Crie credenciais IAM com permissões SES
 *    - Verifique seu domínio no SES
 *    - Adicione as variáveis AWS_* ao seu .env
 * 
 * 3. WhatsApp (Opcional):
 *    - Configure uma instância da Evolution API
 *    - Obtenha as credenciais de acesso
 *    - Adicione as variáveis EVOLUTION_* ao seu .env
 * 
 * 4. Configuração por tenant:
 *    - Cada academia pode ter seu próprio domínio de e-mail
 *    - As configurações são gerenciadas na tabela tenant_notification_settings
 *    - As cores e logo são obtidas da tabela tenants
 */

// Exemplo de .env completo:
export const ENV_EXAMPLE = `
# Sistema de Notificações - E-mail
EMAIL_PROVIDER=resend
RESEND_API_KEY=re_xxxxxxxxxxxxxxxxxxxxxxxxxx
RESEND_FROM_DOMAIN=meusaas.com
RESEND_WEBHOOK_SECRET=whsec_xxxxxxxxxxxxxxxxxxxxxxxxxx

# WhatsApp (Opcional)
EVOLUTION_API_URL=https://your-evolution-api.com
EVOLUTION_API_KEY=your_api_key_here
EVOLUTION_INSTANCE_NAME=apexsaas-whatsapp
EVOLUTION_WEBHOOK_SECRET=your_webhook_secret_here

# Outras configurações existentes...
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
`;

/**
 * Validação das variáveis de ambiente
 */
export function validateEmailEnvironment(): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} {
  const errors: string[] = [];
  const warnings: string[] = [];

  const emailProvider = process.env.EMAIL_PROVIDER;

  if (!emailProvider) {
    errors.push('EMAIL_PROVIDER não definido');
  } else if (!['resend', 'aws_ses'].includes(emailProvider)) {
    errors.push('EMAIL_PROVIDER deve ser "resend" ou "aws_ses"');
  }

  // Validar Resend
  if (emailProvider === 'resend') {
    if (!process.env.RESEND_API_KEY) {
      errors.push('RESEND_API_KEY é obrigatória quando EMAIL_PROVIDER=resend');
    }
    if (!process.env.RESEND_FROM_DOMAIN) {
      warnings.push('RESEND_FROM_DOMAIN não definido, usando padrão');
    }
    if (!process.env.RESEND_WEBHOOK_SECRET) {
      warnings.push('RESEND_WEBHOOK_SECRET não definido, webhooks não funcionarão');
    }
  }

  // Validar AWS SES
  if (emailProvider === 'aws_ses') {
    if (!process.env.AWS_ACCESS_KEY_ID) {
      errors.push('AWS_ACCESS_KEY_ID é obrigatória quando EMAIL_PROVIDER=aws_ses');
    }
    if (!process.env.AWS_SECRET_ACCESS_KEY) {
      errors.push('AWS_SECRET_ACCESS_KEY é obrigatória quando EMAIL_PROVIDER=aws_ses');
    }
    if (!process.env.AWS_REGION) {
      warnings.push('AWS_REGION não definido, usando us-east-1');
    }
    if (!process.env.AWS_SES_FROM_DOMAIN) {
      warnings.push('AWS_SES_FROM_DOMAIN não definido, usando padrão');
    }
  }

  // Validar WhatsApp (opcional)
  const hasWhatsAppUrl = !!process.env.EVOLUTION_API_URL;
  const hasWhatsAppKey = !!process.env.EVOLUTION_API_KEY;

  if (hasWhatsAppUrl && !hasWhatsAppKey) {
    warnings.push('EVOLUTION_API_URL definido mas EVOLUTION_API_KEY ausente');
  }
  if (!hasWhatsAppUrl && hasWhatsAppKey) {
    warnings.push('EVOLUTION_API_KEY definido mas EVOLUTION_API_URL ausente');
  }
  if (!hasWhatsAppUrl && !hasWhatsAppKey) {
    warnings.push('Configurações do WhatsApp ausentes, canal WhatsApp não estará disponível');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * Configurações padrão para desenvolvimento
 */
export const DEV_CONFIG = {
  EMAIL_PROVIDER: 'resend',
  RESEND_FROM_DOMAIN: 'localhost.com',
  // Outras configurações de desenvolvimento...
};

/**
 * Configurações padrão para produção
 */
export const PROD_CONFIG = {
  EMAIL_PROVIDER: 'resend',
  RESEND_FROM_DOMAIN: 'meusaas.com',
  // Outras configurações de produção...
};
