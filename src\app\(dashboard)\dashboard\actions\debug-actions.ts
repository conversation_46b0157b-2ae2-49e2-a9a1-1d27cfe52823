'use server';

import { NotificationService } from '@/services/notifications/core/notification-service';
import { NotificationDispatcher } from '@/services/notifications/channels/notification-dispatcher';
import { NotificationPermissionService } from '@/services/notifications/core/notification-permission-service';
import { getCurrentUser } from '@/services/auth/actions/auth-actions';
import type { CreateNotificationData, NotificationChannel } from '@/services/notifications/types/notification-types';
import { ClassReminderTemplate } from '@/services/notifications/channels/email/templates/class-reminder-template';
import { PaymentReminderTemplate } from '@/services/notifications/channels/email/templates/payment-reminder-template';

/**
 * Cria uma notificação de teste para debug
 * Só funciona em ambiente de desenvolvimento
 */
export async function createTestNotification() {
  // Verificar se está em desenvolvimento
  if (process.env.NODE_ENV !== 'development') {
    return {
      success: false,
      error: 'Função de debug disponível apenas em desenvolvimento'
    };
  }

  try {
    // Obter usuário atual
    const user = await getCurrentUser();
    if (!user) {
      return {
        success: false,
        error: 'Usuário não autenticado'
      };
    }

    // Obter tenant_id do usuário
    const tenantId = (user.app_metadata as any)?.tenant_id;
    if (!tenantId) {
      return {
        success: false,
        error: 'Tenant não encontrado'
      };
    }

    // Dados da notificação de teste
    const testNotificationData: CreateNotificationData = {
      user_id: user.id,
      type: 'system',
      category: 'info',
      priority: 'medium',
      title: '🧪 Notificação de Teste',
      message: 'Esta é uma notificação de teste criada pelo botão de debug. Tudo funcionando perfeitamente!',
      data: {
        debug: true,
        timestamp: new Date().toISOString(),
        environment: process.env.NODE_ENV
      },
      channels: ['in_app']
    };

    // Criar a notificação
    const notificationService = new NotificationService();
    const result = await notificationService.create(tenantId, testNotificationData);

    if (result.success) {
      return {
        success: true,
        message: 'Notificação de teste criada com sucesso!',
        data: result.data
      };
    } else {
      return {
        success: false,
        error: result.error || 'Erro ao criar notificação de teste'
      };
    }

  } catch (error) {
    console.error('Erro ao criar notificação de teste:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Erro desconhecido'
    };
  }
}

/**
 * Cria um dispatcher de teste para um estudante específico
 * Só funciona em ambiente de desenvolvimento
 */
export async function createTestDispatcherForStudent(studentUserId: string) {
  // Verificar se está em desenvolvimento
  if (process.env.NODE_ENV !== 'development') {
    return {
      success: false,
      error: 'Função de debug disponível apenas em desenvolvimento'
    };
  }

  try {
    // Obter usuário atual para pegar o tenant_id
    const user = await getCurrentUser();
    if (!user) {
      return {
        success: false,
        error: 'Usuário não autenticado'
      };
    }

    // Obter tenant_id do usuário
    const tenantId = (user.app_metadata as any)?.tenant_id;
    if (!tenantId) {
      return {
        success: false,
        error: 'Tenant não encontrado'
      };
    }

    // Criar dispatcher de notificações
    const dispatcher = new NotificationDispatcher();

    // Dados da notificação de teste para o estudante específico
    const testDispatchData = {
      tenantId,
      userId: studentUserId,
      type: 'system' as const,
      category: 'info' as const,
      priority: 'medium' as const,
      title: '🎯 Notificação de Teste - Dispatcher',
      message: `Esta é uma notificação de teste enviada via dispatcher para o estudante ${studentUserId}. Sistema funcionando corretamente!`,
      data: {
        debug: true,
        dispatcherTest: true,
        targetStudentId: studentUserId,
        timestamp: new Date().toISOString(),
        environment: process.env.NODE_ENV,
        sentBy: user.id
      },
      channels: ['in_app', 'email'] as NotificationChannel[]
    };

    // Enviar através do dispatcher
    const result = await dispatcher.dispatch(testDispatchData);

    if (result.success) {
      return {
        success: true,
        message: `Notificação de teste enviada via dispatcher para o estudante ${studentUserId}!`,
        data: {
          notificationId: result.notificationId,
          channelResults: result.channelResults,
          errors: result.errors
        }
      };
    } else {
      return {
        success: false,
        error: `Erro no dispatcher: ${result.errors.join(', ')}`
      };
    }

  } catch (error) {
    console.error('Erro ao criar dispatcher de teste:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Erro desconhecido'
    };
  }
}

/**
 * Cria um dispatcher de teste para lembrete de aula
 * Só funciona em ambiente de desenvolvimento
 */
export async function createTestClassReminderDispatcher(studentUserId: string) {
  // Verificar se está em desenvolvimento
  if (process.env.NODE_ENV !== 'development') {
    return {
      success: false,
      error: 'Função de debug disponível apenas em desenvolvimento'
    };
  }

  try {
    // Obter usuário atual para pegar o tenant_id
    const user = await getCurrentUser();
    if (!user) {
      return {
        success: false,
        error: 'Usuário não autenticado'
      };
    }

    // Obter tenant_id do usuário
    const tenantId = (user.app_metadata as any)?.tenant_id;
    if (!tenantId) {
      return {
        success: false,
        error: 'Tenant não encontrado'
      };
    }

    // Criar dispatcher de notificações
    const dispatcher = new NotificationDispatcher();

    // Dados de teste para o lembrete de aula
    const classReminderData = {
      tenantId,
      userId: studentUserId,
      type: 'class' as const,
      category: 'reminder' as const,
      priority: 'high' as const,
      title: '🥋 Lembrete de Aula - Teste',
      message: 'Você tem uma aula de Jiu-Jitsu hoje às 19:00 com o Professor João Silva.',
      data: {
        debug: true,
        classReminderTest: true,
        targetStudentId: studentUserId,
        timestamp: new Date().toISOString(),
        environment: process.env.NODE_ENV,
        sentBy: user.id,
        // Dados específicos do template de aula
        templateData: {
          academyName: 'Academia Teste',
          studentName: 'Estudante Teste',
          className: 'Jiu-Jitsu Gi',
          instructorName: 'Professor João Silva',
          classDate: new Date().toISOString().split('T')[0],
          classTime: '19:00',
          location: 'Tatame Principal',
          duration: 90,
          maxStudents: 20,
          currentEnrollments: 15,
          reminderType: 'today',
          requiresEquipment: ['Kimono', 'Faixa', 'Protetor bucal'],
          specialInstructions: 'Aula focada em técnicas de guarda fechada'
        }
      },
      channels: ['in_app', 'email'] as NotificationChannel[]
    };

    // Enviar através do dispatcher
    const result = await dispatcher.dispatch(classReminderData);

    if (result.success) {
      return {
        success: true,
        message: `Lembrete de aula de teste enviado para o estudante ${studentUserId}!`,
        data: {
          notificationId: result.notificationId,
          channelResults: result.channelResults,
          errors: result.errors
        }
      };
    } else {
      return {
        success: false,
        error: `Erro no dispatcher: ${result.errors.join(', ')}`
      };
    }

  } catch (error) {
    console.error('Erro ao criar dispatcher de lembrete de aula:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Erro desconhecido'
    };
  }
}

/**
 * Cria um dispatcher de teste para lembrete de pagamento
 * Só funciona em ambiente de desenvolvimento
 */
export async function createTestPaymentReminderDispatcher(studentUserId: string) {
  // Verificar se está em desenvolvimento
  if (process.env.NODE_ENV !== 'development') {
    return {
      success: false,
      error: 'Função de debug disponível apenas em desenvolvimento'
    };
  }

  try {
    // Obter usuário atual para pegar o tenant_id
    const user = await getCurrentUser();
    if (!user) {
      return {
        success: false,
        error: 'Usuário não autenticado'
      };
    }

    // Obter tenant_id do usuário
    const tenantId = (user.app_metadata as any)?.tenant_id;
    if (!tenantId) {
      return {
        success: false,
        error: 'Tenant não encontrado'
      };
    }

    // Criar dispatcher de notificações
    const dispatcher = new NotificationDispatcher();

    // Dados de teste para o lembrete de pagamento
    const paymentReminderData = {
      tenantId,
      userId: studentUserId,
      type: 'payment' as const,
      category: 'info' as const,
      priority: 'high' as const,
      title: '💳 Lembrete de Pagamento - Teste',
      message: 'Sua mensalidade no valor de R$ 150,00 vence em 3 dias (15/12/2024).',
      data: {
        debug: true,
        paymentReminderTest: true,
        targetStudentId: studentUserId,
        timestamp: new Date().toISOString(),
        environment: process.env.NODE_ENV,
        sentBy: user.id,
        // Dados específicos do template de pagamento
        templateData: {
          academyName: 'Academia Teste',
          studentName: 'Estudante Teste',
          amount: 150.00,
          dueDate: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 3 dias a partir de hoje
          planName: 'Plano Mensal - Jiu-Jitsu',
          paymentMethod: 'Cartão de Crédito',
          currency: 'BRL',
          lateFee: 15.00,
          gracePeriod: 5
        }
      },
      channels: ['in_app', 'email'] as NotificationChannel[]
    };

    // Enviar através do dispatcher
    const result = await dispatcher.dispatch(paymentReminderData);

    if (result.success) {
      return {
        success: true,
        message: `Lembrete de pagamento de teste enviado para o estudante ${studentUserId}!`,
        data: {
          notificationId: result.notificationId,
          channelResults: result.channelResults,
          errors: result.errors
        }
      };
    } else {
      return {
        success: false,
        error: `Erro no dispatcher: ${result.errors.join(', ')}`
      };
    }

  } catch (error) {
    console.error('Erro ao criar dispatcher de lembrete de pagamento:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Erro desconhecido'
    };
  }
}

/**
 * Testa as permissões de notificação para um usuário específico
 * Só funciona em ambiente de desenvolvimento
 */
export async function testNotificationPermissions(studentUserId: string) {
  // Verificar se está em desenvolvimento
  if (process.env.NODE_ENV !== 'development') {
    return {
      success: false,
      error: 'Função de debug disponível apenas em desenvolvimento'
    };
  }

  try {
    // Obter usuário atual para pegar o tenant_id
    const user = await getCurrentUser();
    if (!user) {
      return {
        success: false,
        error: 'Usuário não autenticado'
      };
    }

    // Obter tenant_id do usuário
    const tenantId = (user.app_metadata as any)?.tenant_id;
    if (!tenantId) {
      return {
        success: false,
        error: 'Tenant não encontrado'
      };
    }

    // Criar serviço de permissões
    const permissionService = new NotificationPermissionService();

    // Testar permissões para diferentes tipos e canais
    const tests = [
      { type: 'payment' as const, channel: 'email' as const },
      { type: 'payment' as const, channel: 'in_app' as const },
      { type: 'class' as const, channel: 'email' as const },
      { type: 'class' as const, channel: 'in_app' as const },
      { type: 'system' as const, channel: 'email' as const },
    ];

    const results = [];

    for (const test of tests) {
      const permission = await permissionService.checkPermission({
        tenantId,
        userId: studentUserId,
        notificationType: test.type,
        channel: test.channel
      });

      results.push({
        type: test.type,
        channel: test.channel,
        allowed: permission.allowed,
        reason: permission.reason,
        source: permission.source
      });
    }

    return {
      success: true,
      message: 'Teste de permissões concluído',
      data: {
        tenantId,
        studentUserId,
        results
      }
    };

  } catch (error) {
    console.error('Erro ao testar permissões:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Erro desconhecido'
    };
  }
}

/**
 * Testa especificamente o lembrete de pagamento com verificação de permissões
 * Só funciona em ambiente de desenvolvimento
 */
export async function testPaymentReminderWithPermissions(studentUserId: string) {
  // Verificar se está em desenvolvimento
  if (process.env.NODE_ENV !== 'development') {
    return {
      success: false,
      error: 'Função de debug disponível apenas em desenvolvimento'
    };
  }

  try {
    // Obter usuário atual para pegar o tenant_id
    const user = await getCurrentUser();
    if (!user) {
      return {
        success: false,
        error: 'Usuário não autenticado'
      };
    }

    // Obter tenant_id do usuário
    const tenantId = (user.app_metadata as any)?.tenant_id;
    if (!tenantId) {
      return {
        success: false,
        error: 'Tenant não encontrado'
      };
    }

    // Criar dispatcher de notificações
    const dispatcher = new NotificationDispatcher();

    // Testar o sendPaymentReminder que agora usa verificação de permissões
    const result = await dispatcher.sendPaymentReminder({
      tenantId,
      userId: studentUserId,
      studentName: 'Estudante Teste',
      amount: 150.00,
      dueDate: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      planName: 'Plano Mensal - Jiu-Jitsu',
      paymentMethod: 'Cartão de Crédito'
      // Não especificamos channels, então usará o padrão ['in_app', 'email']
    });

    return {
      success: true,
      message: 'Teste de lembrete de pagamento com permissões concluído!',
      data: {
        tenantId,
        studentUserId,
        notificationId: result.notificationId,
        channelResults: result.channelResults,
        errors: result.errors,
        wasEmailBlocked: !result.channelResults.email?.success,
        emailBlockReason: result.channelResults.email?.error
      }
    };

  } catch (error) {
    console.error('Erro ao testar lembrete de pagamento:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Erro desconhecido'
    };
  }
}
